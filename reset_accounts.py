#!/usr/bin/env python3
"""
Script to reset all account statuses to 'processing'
This will:
- Set all account status to 'processing' 
- Remove 'last_tried' field from all accounts
- Keep reward points intact
Usage: python reset_accounts.py
"""

import json
import os
import sys
from loguru import logger

def read_accounts_json_safe():
    """Safely read accounts.json with multiple encoding attempts"""
    try:
        # Try different encodings to handle manual edits
        encodings = ['utf-8', 'utf-8-sig', 'ascii', 'latin-1', 'cp1252']
        
        for encoding in encodings:
            try:
                with open('accounts.json', 'r', encoding=encoding) as f:
                    content = f.read().strip()
                    # Remove BOM if present
                    if content.startswith('\ufeff'):
                        content = content[1:]
                    # Remove any trailing commas before closing brackets (common manual edit error)
                    content = content.replace(',\n]', '\n]').replace(',]', ']')
                    accounts = json.loads(content)
                    logger.debug(f"Successfully read accounts.json with {encoding} encoding")
                    return accounts
            except (UnicodeDecodeError, json.JSONDecodeError) as e:
                logger.debug(f"Failed to read with {encoding}: {e}")
                continue
        
        logger.error("Could not read accounts.json with any encoding!")
        return None
        
    except FileNotFoundError:
        logger.error("accounts.json file not found!")
        return None
    except Exception as e:
        logger.error(f"Unexpected error reading accounts.json: {e}")
        return None

def write_accounts_json_safe(accounts):
    """Safely write accounts.json with proper encoding"""
    try:
        # Always write with UTF-8 encoding for consistency
        with open('accounts.json', 'w', encoding='utf-8') as f:
            json.dump(accounts, f, indent=2, ensure_ascii=False)
        logger.debug("Successfully wrote accounts.json")
        return True
    except Exception as e:
        logger.error(f"Failed to write accounts.json: {e}")
        return False

def reset_all_accounts():
    """Reset all accounts to processing status"""
    filename = 'accounts.json'
    
    if not os.path.exists(filename):
        logger.error(f"File {filename} not found!")
        return False
    
    logger.info(f"Reading {filename}...")
    
    # Read accounts
    accounts = read_accounts_json_safe()
    if accounts is None:
        logger.error("Could not read accounts.json!")
        return False
    
    # Show current status
    total = len(accounts)
    success_count = sum(1 for acc in accounts if acc.get('status') == 'success')
    processing_count = sum(1 for acc in accounts if acc.get('status') == 'processing')
    
    logger.info(f"Current status:")
    logger.info(f"  Total accounts: {total}")
    logger.info(f"  Success: {success_count}")
    logger.info(f"  Processing: {processing_count}")
    
    # Create backup
    backup_filename = f"{filename}.backup_before_reset"
    try:
        with open(backup_filename, 'w', encoding='utf-8') as f:
            json.dump(accounts, f, indent=2, ensure_ascii=False)
        logger.info(f"✅ Created backup: {backup_filename}")
    except Exception as e:
        logger.warning(f"Could not create backup: {e}")
    
    # Reset all accounts
    reset_count = 0
    last_tried_removed = 0
    
    for account in accounts:
        # Reset status to processing
        if account.get('status') != 'processing':
            account['status'] = 'processing'
            reset_count += 1
        
        # Remove last_tried field if exists
        if 'last_tried' in account:
            del account['last_tried']
            last_tried_removed += 1
        
        # Keep reward points intact - don't reset them
        # This preserves the points earned from successful runs
    
    # Write back
    if write_accounts_json_safe(accounts):
        logger.success(f"✅ Successfully reset accounts!")
        logger.info(f"📊 Reset summary:")
        logger.info(f"  - Status changed to 'processing': {reset_count} accounts")
        logger.info(f"  - 'last_tried' field removed: {last_tried_removed} accounts")
        logger.info(f"  - Reward points preserved for all accounts")
        
        # Show final status
        logger.info(f"📋 Final status:")
        logger.info(f"  - Total accounts: {total}")
        logger.info(f"  - All accounts now have status: 'processing'")
        logger.info(f"  - Ready to run from the beginning!")
        
        return True
    else:
        logger.error("❌ Failed to write updated accounts.json")
        return False

def main():
    logger.info("🔄 Account Status Reset Tool")
    logger.info("=" * 40)
    
    # Confirm action
    try:
        confirm = input("⚠️  This will reset ALL accounts to 'processing' status.\n"
                       "   Reward points will be preserved.\n"
                       "   Continue? (y/N): ").strip().lower()
        
        if confirm not in ['y', 'yes']:
            logger.info("❌ Operation cancelled by user")
            return False
    except KeyboardInterrupt:
        logger.info("\n❌ Operation cancelled by user")
        return False
    
    success = reset_all_accounts()
    
    if success:
        logger.success("🎉 All accounts have been reset successfully!")
        logger.info("💡 You can now run the worker script to process all accounts again")
    else:
        logger.error("❌ Failed to reset accounts")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
