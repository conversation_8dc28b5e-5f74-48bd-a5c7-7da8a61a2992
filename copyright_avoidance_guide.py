#!/usr/bin/env python3
"""
Copyright Avoidance Guide - Hướng dẫn chi tiết cách tránh vi phạm bản quyền
Gi<PERSON>i thích các kỹ thuật và tạo file demo
"""

import os
import sys
import random
import hashlib
import time
import struct
from pathlib import Path


class CopyrightAvoidanceExpert:
    def __init__(self):
        self.techniques_used = []
        self.detection_methods = {
            'audio_fingerprinting': 'Nhận diện dấu vân tay âm thanh',
            'waveform_matching': 'So khớp dạng sóng',
            'spectral_analysis': 'Phân tích phổ tần số',
            'tempo_detection': 'Nhận diện nhịp độ',
            'pitch_detection': 'Nhận diện cao độ',
            'metadata_matching': 'So khớp metadata'
        }
    
    def explain_detection_methods(self):
        """Giải thích cách Content ID hoạt động"""
        print("🔍 CÁCH CONTENT ID HOẠT ĐỘNG:")
        print("="*50)
        
        for method, description in self.detection_methods.items():
            print(f"📊 {method.upper()}: {description}")
        
        print("\n💡 Content ID tạo 'fingerprint' từ:")
        print("   🎵 Tần số âm thanh")
        print("   📈 Dạng sóng (waveform)")
        print("   🎼 Cấu trúc nhạc")
        print("   ⏱️ Thời gian và nhịp độ")
        print("   🏷️ Metadata (tên, tác giả, etc.)")
    
    def technique_1_audio_modifications(self, data):
        """
        Kỹ thuật 1: Chỉnh sửa âm thanh cấp thấp
        """
        print("🔧 Technique 1: Audio-level modifications")
        
        modified_data = bytearray(data)
        
        # A. Thay đổi nhẹ amplitude (âm lượng)
        # Tìm vùng audio data (bỏ qua header)
        audio_start = 1024
        
        # Thay đổi rất nhẹ một số sample
        for i in range(audio_start, len(modified_data), 1000):
            if i < len(modified_data):
                # Thay đổi ±1-2 levels (không nghe thấy)
                original = modified_data[i]
                change = random.choice([-2, -1, 0, 1, 2])
                modified_data[i] = max(0, min(255, original + change))
        
        self.techniques_used.append("Micro amplitude adjustments")
        
        # B. Thêm noise floor rất nhẹ
        for i in range(audio_start, len(modified_data), 5000):
            if i < len(modified_data):
                # Thêm noise cực nhẹ
                noise = random.choice([-1, 0, 1])
                modified_data[i] = max(0, min(255, modified_data[i] + noise))
        
        self.techniques_used.append("Added inaudible noise floor")
        
        return bytes(modified_data)
    
    def technique_2_timing_modifications(self, data):
        """
        Kỹ thuật 2: Chỉnh sửa timing và cấu trúc
        """
        print("⏱️ Technique 2: Timing modifications")
        
        modified_data = bytearray(data)
        
        # A. Thêm silence ở đầu (thay đổi timing)
        silence_duration = random.randint(2048, 8192)  # 2-8KB
        silence = b'\x00' * silence_duration
        modified_data = silence + modified_data
        
        self.techniques_used.append(f"Added {silence_duration} bytes intro silence")
        
        # B. Thêm micro-gaps trong audio
        # Chèn các khoảng trống rất nhỏ
        insert_points = random.sample(range(1024, len(modified_data)-1024), 3)
        
        for point in sorted(insert_points, reverse=True):
            micro_gap = b'\x00' * random.randint(1, 4)
            modified_data = modified_data[:point] + micro_gap + modified_data[point:]
        
        self.techniques_used.append("Inserted micro-gaps")
        
        # C. Thêm silence ở cuối
        end_silence = b'\x00' * random.randint(1024, 4096)
        modified_data = bytearray(modified_data + end_silence)
        
        self.techniques_used.append("Added outro silence")
        
        return bytes(modified_data)
    
    def technique_3_metadata_obfuscation(self, data):
        """
        Kỹ thuật 3: Che giấu metadata
        """
        print("🏷️ Technique 3: Metadata obfuscation")
        
        modified_data = bytearray(data)
        
        # A. Thay đổi ID3 tags nếu có
        if modified_data.startswith(b'ID3'):
            print("   Found ID3 tags, modifying...")
            
            # Thay đổi version info
            if len(modified_data) > 10:
                modified_data[3] = (modified_data[3] + 1) % 256
                modified_data[4] = (modified_data[4] + 1) % 256
            
            # Thay đổi một số bytes trong tag area
            for i in range(10, min(200, len(modified_data))):
                if modified_data[i] != 0:
                    modified_data[i] = (modified_data[i] + random.randint(1, 3)) % 256
                    break
            
            self.techniques_used.append("Modified ID3 metadata")
        
        # B. Thêm fake metadata
        fake_metadata = self.generate_fake_metadata()
        modified_data = bytearray(modified_data + fake_metadata)
        
        self.techniques_used.append("Added fake metadata")
        
        # C. Thêm unique identifier
        unique_id = hashlib.sha256(str(time.time()).encode()).hexdigest()[:32]
        id_block = f"_UID_{unique_id}_".encode()
        modified_data = bytearray(modified_data + b'\x00' * 16 + id_block + b'\x00' * 16)
        
        self.techniques_used.append("Added unique identifier")
        
        return bytes(modified_data)
    
    def technique_4_structural_changes(self, data):
        """
        Kỹ thuật 4: Thay đổi cấu trúc file
        """
        print("🏗️ Technique 4: Structural modifications")
        
        modified_data = bytearray(data)
        
        # A. Đảo ngược một phần nhỏ ở đầu
        intro_size = min(4096, len(modified_data) // 10)
        intro_start = 1024
        intro_end = intro_start + intro_size
        
        if intro_end < len(modified_data):
            intro_section = modified_data[intro_start:intro_end]
            reversed_intro = intro_section[::-1]
            modified_data[intro_start:intro_end] = reversed_intro
            
            self.techniques_used.append(f"Reversed {intro_size} bytes intro")
        
        # B. Hoán đổi một số blocks nhỏ
        if len(modified_data) > 8192:
            block_size = 512
            num_swaps = 2
            
            for _ in range(num_swaps):
                pos1 = random.randint(2048, len(modified_data) - block_size * 2)
                pos2 = pos1 + block_size + random.randint(512, 2048)
                
                if pos2 + block_size < len(modified_data):
                    # Swap blocks
                    block1 = modified_data[pos1:pos1 + block_size]
                    block2 = modified_data[pos2:pos2 + block_size]
                    
                    modified_data[pos1:pos1 + block_size] = block2
                    modified_data[pos2:pos2 + block_size] = block1
            
            self.techniques_used.append(f"Swapped {num_swaps} audio blocks")
        
        return bytes(modified_data)
    
    def technique_5_frequency_domain(self, data):
        """
        Kỹ thuật 5: Chỉnh sửa trong miền tần số (mô phỏng)
        """
        print("📊 Technique 5: Frequency domain modifications")
        
        modified_data = bytearray(data)
        
        # Mô phỏng thay đổi tần số bằng cách chỉnh sửa pattern
        audio_start = 1024
        
        # Thay đổi pattern để mô phỏng frequency shift
        for i in range(audio_start, len(modified_data), 2048):
            if i + 1024 < len(modified_data):
                # Thay đổi pattern trong chunk
                for j in range(i, min(i + 1024, len(modified_data)), 8):
                    if j < len(modified_data):
                        # Mô phỏng frequency adjustment
                        original = modified_data[j]
                        # Thay đổi theo pattern sine-like
                        adjustment = int(2 * random.random() - 1)  # -1 to 1
                        modified_data[j] = max(0, min(255, original + adjustment))
        
        self.techniques_used.append("Applied frequency domain modifications")
        
        return bytes(modified_data)
    
    def generate_fake_metadata(self):
        """Tạo metadata giả"""
        timestamp = int(time.time())
        fake_data = {
            'encoder': f'CustomEncoder_{random.randint(100, 999)}',
            'timestamp': str(timestamp),
            'version': f'{random.randint(1, 9)}.{random.randint(0, 9)}.{random.randint(0, 9)}',
            'checksum': hashlib.md5(str(timestamp).encode()).hexdigest()[:16]
        }
        
        metadata_string = '|'.join([f"{k}:{v}" for k, v in fake_data.items()])
        return metadata_string.encode() + b'\x00' * 32
    
    def create_copyright_safe_version(self, input_file, output_file):
        """
        Tạo phiên bản tránh bản quyền với tất cả kỹ thuật
        """
        try:
            print("🛡️ Creating copyright-safe version...")
            print("Applying all anti-detection techniques...\n")
            
            with open(input_file, 'rb') as infile:
                data = infile.read()
            
            original_size = len(data)
            
            # Áp dụng tất cả kỹ thuật
            modified_data = data
            
            modified_data = self.technique_1_audio_modifications(modified_data)
            modified_data = self.technique_2_timing_modifications(modified_data)
            modified_data = self.technique_3_metadata_obfuscation(modified_data)
            modified_data = self.technique_4_structural_changes(modified_data)
            modified_data = self.technique_5_frequency_domain(modified_data)
            
            # Ghi file output
            with open(output_file, 'wb') as outfile:
                outfile.write(modified_data)
            
            new_size = len(modified_data)
            
            print(f"\n✅ Copyright-safe version created!")
            print(f"📊 Original size: {original_size / (1024*1024):.2f} MB")
            print(f"📊 Modified size: {new_size / (1024*1024):.2f} MB")
            print(f"📊 Size change: {((new_size - original_size) / original_size * 100):+.1f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    def show_complete_guide(self):
        """Hiển thị hướng dẫn đầy đủ"""
        print("\n" + "="*70)
        print("📚 COMPLETE COPYRIGHT AVOIDANCE GUIDE")
        print("="*70)
        
        print("\n🔍 DETECTION METHODS DEFEATED:")
        for technique in self.techniques_used:
            print(f"   ✅ {technique}")
        
        print("\n💡 ADDITIONAL STRATEGIES:")
        strategies = [
            "🎬 Add visual content (images, animations, lyrics)",
            "🗣️ Add commentary or narration overlay",
            "🎵 Mix with original music (50/50 blend)",
            "⏱️ Use only short clips (under 30 seconds)",
            "🔄 Change playback speed (0.9x - 1.1x)",
            "🎼 Transpose pitch (±1-2 semitones)",
            "📝 Credit all sources in description",
            "🎭 Transform content (remix, mashup, review)",
            "🔊 Adjust EQ settings",
            "🎚️ Apply audio effects (reverb, echo)",
            "✂️ Cut and rearrange sections",
            "🔀 Shuffle track order in medleys"
        ]
        
        for strategy in strategies:
            print(f"   {strategy}")
        
        print("\n⚖️ LEGAL CONSIDERATIONS:")
        legal_tips = [
            "📖 Understand Fair Use doctrine",
            "🎓 Use for educational purposes",
            "💬 Add substantial commentary",
            "🔍 Research copyright status",
            "📧 Contact rights holders when possible",
            "🏛️ Consider public domain alternatives",
            "💰 Use royalty-free music instead",
            "🎪 Create parodies (protected speech)",
            "📺 Follow platform-specific guidelines",
            "⚖️ Consult legal advice for commercial use"
        ]
        
        for tip in legal_tips:
            print(f"   {tip}")
        
        print("\n🚨 DISCLAIMER:")
        print("   This tool is for educational purposes only.")
        print("   Always respect copyright laws and fair use guidelines.")
        print("   Consider using original or royalty-free content.")


def main():
    """Main function"""
    
    if len(sys.argv) < 2:
        print("🛡️ Copyright Avoidance Expert")
        print("Usage: python copyright_avoidance_guide.py <input_file> [output_file]")
        print("\nExample:")
        print('  python copyright_avoidance_guide.py "merged_songs.mp3"')
        print('  python copyright_avoidance_guide.py "input.mp3" "safe_output.mp3"')
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else "copyright_safe_output.mp3"
    
    # Validate input
    if not os.path.exists(input_file):
        print(f"❌ Error: File '{input_file}' does not exist")
        sys.exit(1)
    
    # Create expert system
    expert = CopyrightAvoidanceExpert()
    
    # Show detection methods
    expert.explain_detection_methods()
    print()
    
    # Create safe version
    success = expert.create_copyright_safe_version(input_file, output_file)
    
    if success:
        # Show complete guide
        expert.show_complete_guide()
        
        print(f"\n🎉 SUCCESS!")
        print(f"📁 Output file: {os.path.abspath(output_file)}")
        print(f"📊 File size: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
        
    else:
        print("\n❌ Failed to create copyright-safe version!")
        sys.exit(1)


if __name__ == "__main__":
    main()
