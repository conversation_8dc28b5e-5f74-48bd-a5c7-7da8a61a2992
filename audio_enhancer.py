#!/usr/bin/env python3
"""
Audio Enhancement Tool - <PERSON>h chỉnh chất lượng âm thanh và tránh vi phạm bản quyền
Features:
- Crossfade transitions
- Volume normalization  
- Pitch shifting (tránh Content ID)
- Speed adjustment
- Audio effects
- Metadata removal
"""

import os
import sys
import random
from pathlib import Path

try:
    from pydub import AudioSegment
    from pydub.effects import normalize, compress_dynamic_range
    from pydub.utils import make_chunks
except ImportError:
    print("❌ Error: pydub library is required")
    print("Install with: pip install pydub")
    sys.exit(1)


class AudioEnhancer:
    def __init__(self):
        self.sample_rate = 44100
        self.bit_depth = 16
        
    def load_audio(self, file_path):
        """Load audio file with error handling"""
        try:
            if file_path.lower().endswith('.mp3'):
                audio = AudioSegment.from_mp3(file_path)
            elif file_path.lower().endswith('.wav'):
                audio = AudioSegment.from_wav(file_path)
            else:
                audio = AudioSegment.from_file(file_path)
            
            print(f"✅ Loaded: {os.path.basename(file_path)}")
            print(f"   Duration: {len(audio)/1000:.2f}s, Channels: {audio.channels}, Sample Rate: {audio.frame_rate}Hz")
            return audio
        except Exception as e:
            print(f"❌ Error loading {file_path}: {e}")
            return None
    
    def normalize_volume(self, audio, target_dBFS=-20.0):
        """Normalize audio volume"""
        print(f"🔊 Normalizing volume to {target_dBFS} dBFS...")
        normalized = normalize(audio)
        # Adjust to target level
        change_in_dBFS = target_dBFS - normalized.dBFS
        return normalized.apply_gain(change_in_dBFS)
    
    def add_crossfade(self, audio1, audio2, fade_duration=3000):
        """Add smooth crossfade between two audio segments"""
        print(f"🎵 Adding {fade_duration}ms crossfade...")
        return audio1.append(audio2, crossfade=fade_duration)
    
    def pitch_shift(self, audio, semitones=0):
        """
        Shift pitch to avoid Content ID detection
        Positive values = higher pitch, Negative = lower pitch
        """
        if semitones == 0:
            return audio
            
        print(f"🎼 Shifting pitch by {semitones} semitones...")
        # Calculate new sample rate for pitch shift
        new_sample_rate = int(audio.frame_rate * (2.0 ** (semitones / 12.0)))
        
        # Change frame rate without resampling (pitch shift)
        pitched_audio = audio._spawn(audio.raw_data, overrides={"frame_rate": new_sample_rate})
        
        # Convert back to original sample rate
        return pitched_audio.set_frame_rate(audio.frame_rate)
    
    def speed_change(self, audio, speed_factor=1.0):
        """
        Change playback speed
        speed_factor > 1.0 = faster, < 1.0 = slower
        """
        if speed_factor == 1.0:
            return audio
            
        print(f"⚡ Changing speed by factor {speed_factor}...")
        # Change frame rate to adjust speed
        new_frame_rate = int(audio.frame_rate * speed_factor)
        return audio._spawn(audio.raw_data, overrides={"frame_rate": new_frame_rate}).set_frame_rate(audio.frame_rate)
    
    def add_silence(self, audio, start_silence=1000, end_silence=1000):
        """Add silence at beginning and end"""
        print(f"🔇 Adding silence: {start_silence}ms start, {end_silence}ms end...")
        silence_start = AudioSegment.silent(duration=start_silence)
        silence_end = AudioSegment.silent(duration=end_silence)
        return silence_start + audio + silence_end
    
    def apply_compression(self, audio, threshold=-20.0, ratio=4.0):
        """Apply dynamic range compression"""
        print(f"🎚️ Applying compression (threshold: {threshold}dB, ratio: {ratio}:1)...")
        return compress_dynamic_range(audio, threshold=threshold, ratio=ratio)
    
    def remove_metadata(self, audio):
        """Remove all metadata to avoid detection"""
        print("🗑️ Removing metadata...")
        # Create new audio segment without metadata
        return AudioSegment(
            audio.raw_data,
            frame_rate=audio.frame_rate,
            sample_width=audio.sample_width,
            channels=audio.channels
        )
    
    def enhance_merged_audio(self, input_file, output_file, enhancement_options=None):
        """
        Main enhancement function with copyright-safe modifications
        """
        if enhancement_options is None:
            enhancement_options = {
                'normalize': True,
                'pitch_shift': random.uniform(-0.5, 0.5),  # Random slight pitch change
                'speed_change': random.uniform(0.98, 1.02),  # Random slight speed change
                'add_silence': True,
                'compression': True,
                'remove_metadata': True
            }
        
        print("🎵 Starting audio enhancement...")
        print(f"📁 Input: {input_file}")
        print(f"📁 Output: {output_file}")
        print()
        
        # Load audio
        audio = self.load_audio(input_file)
        if audio is None:
            return False
        
        original_duration = len(audio) / 1000
        print(f"📊 Original duration: {original_duration:.2f} seconds")
        print()
        
        # Apply enhancements
        if enhancement_options.get('normalize', True):
            audio = self.normalize_volume(audio)
        
        if enhancement_options.get('pitch_shift', 0) != 0:
            audio = self.pitch_shift(audio, enhancement_options['pitch_shift'])
        
        if enhancement_options.get('speed_change', 1.0) != 1.0:
            audio = self.speed_change(audio, enhancement_options['speed_change'])
        
        if enhancement_options.get('add_silence', False):
            audio = self.add_silence(audio, 2000, 2000)  # 2 seconds each
        
        if enhancement_options.get('compression', False):
            audio = self.apply_compression(audio)
        
        if enhancement_options.get('remove_metadata', True):
            audio = self.remove_metadata(audio)
        
        # Export enhanced audio
        print("💾 Exporting enhanced audio...")
        try:
            if output_file.lower().endswith('.wav'):
                audio.export(output_file, format="wav", parameters=["-ar", "44100", "-ac", "2"])
            elif output_file.lower().endswith('.mp3'):
                audio.export(output_file, format="mp3", bitrate="320k")
            else:
                audio.export(output_file, format="wav")
            
            final_duration = len(audio) / 1000
            file_size = os.path.getsize(output_file) / (1024*1024)
            
            print("✅ Enhancement completed successfully!")
            print(f"📊 Final duration: {final_duration:.2f} seconds")
            print(f"📊 File size: {file_size:.2f} MB")
            print(f"📁 Output file: {os.path.abspath(output_file)}")
            
            return True
            
        except Exception as e:
            print(f"❌ Export failed: {e}")
            return False


def main():
    """Main function with command line interface"""
    
    if len(sys.argv) < 3:
        print("🎵 Audio Enhancement Tool")
        print("Usage: python audio_enhancer.py <input_file> <output_file> [options]")
        print("\nOptions:")
        print("  --pitch <semitones>     Pitch shift (-2 to +2)")
        print("  --speed <factor>        Speed change (0.8 to 1.2)")
        print("  --no-normalize          Skip volume normalization")
        print("  --no-silence            Skip adding silence")
        print("  --compression           Apply dynamic compression")
        print("\nExample:")
        print('  python audio_enhancer.py "merged_songs.mp3" "enhanced_songs.wav"')
        print('  python audio_enhancer.py "input.mp3" "output.wav" --pitch 0.3 --speed 1.02')
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    # Parse options
    options = {
        'normalize': True,
        'pitch_shift': random.uniform(-0.3, 0.3),  # Random slight change
        'speed_change': random.uniform(0.99, 1.01),  # Random slight change
        'add_silence': True,
        'compression': False,
        'remove_metadata': True
    }
    
    i = 3
    while i < len(sys.argv):
        if sys.argv[i] == '--pitch' and i + 1 < len(sys.argv):
            options['pitch_shift'] = float(sys.argv[i + 1])
            i += 2
        elif sys.argv[i] == '--speed' and i + 1 < len(sys.argv):
            options['speed_change'] = float(sys.argv[i + 1])
            i += 2
        elif sys.argv[i] == '--no-normalize':
            options['normalize'] = False
            i += 1
        elif sys.argv[i] == '--no-silence':
            options['add_silence'] = False
            i += 1
        elif sys.argv[i] == '--compression':
            options['compression'] = True
            i += 1
        else:
            i += 1
    
    # Validate input file
    if not os.path.exists(input_file):
        print(f"❌ Error: File '{input_file}' does not exist")
        sys.exit(1)
    
    # Create enhancer and process
    enhancer = AudioEnhancer()
    success = enhancer.enhance_merged_audio(input_file, output_file, options)
    
    if success:
        print("\n🎉 Audio enhancement completed!")
        print("💡 Tips for YouTube upload:")
        print("   - The audio has been slightly modified to avoid Content ID")
        print("   - Consider adding visual content or commentary")
        print("   - Check YouTube's fair use guidelines")
    else:
        print("\n❌ Enhancement failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
