#!/usr/bin/env python3
"""
Script to merge two MP3 files into one WAV file
Requires: pydub library
Install with: pip install pydub
"""

import os
import sys
from pydub import AudioSegment
from pathlib import Path


def merge_mp3_to_wav(mp3_file1, mp3_file2, output_wav, crossfade_duration=0):
    """
    Merge two MP3 files into one WAV file
    
    Args:
        mp3_file1 (str): Path to first MP3 file
        mp3_file2 (str): Path to second MP3 file  
        output_wav (str): Path for output WAV file
        crossfade_duration (int): Crossfade duration in milliseconds (default: 0)
    """
    try:
        print(f"Loading {mp3_file1}...")
        audio1 = AudioSegment.from_mp3(mp3_file1)
        
        print(f"Loading {mp3_file2}...")
        audio2 = AudioSegment.from_mp3(mp3_file2)
        
        print(f"Audio 1 duration: {len(audio1)/1000:.2f} seconds")
        print(f"Audio 2 duration: {len(audio2)/1000:.2f} seconds")
        
        # Merge audio files
        if crossfade_duration > 0:
            print(f"Merging with {crossfade_duration}ms crossfade...")
            merged_audio = audio1.append(audio2, crossfade=crossfade_duration)
        else:
            print("Merging without crossfade...")
            merged_audio = audio1 + audio2
        
        print(f"Total duration: {len(merged_audio)/1000:.2f} seconds")
        
        # Export as WAV
        print(f"Exporting to {output_wav}...")
        merged_audio.export(output_wav, format="wav")
        
        print("✅ Successfully merged MP3 files to WAV!")
        print(f"Output file: {os.path.abspath(output_wav)}")
        print(f"File size: {os.path.getsize(output_wav) / (1024*1024):.2f} MB")
        
    except FileNotFoundError as e:
        print(f"❌ Error: File not found - {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


def main():
    """Main function with command line interface"""
    
    if len(sys.argv) < 4:
        print("Usage: python merge_mp3_to_wav.py <mp3_file1> <mp3_file2> <output_wav> [crossfade_ms]")
        print("\nExample:")
        print("  python merge_mp3_to_wav.py song1.mp3 song2.mp3 merged.wav")
        print("  python merge_mp3_to_wav.py song1.mp3 song2.mp3 merged.wav 1000")
        sys.exit(1)
    
    mp3_file1 = sys.argv[1]
    mp3_file2 = sys.argv[2]
    output_wav = sys.argv[3]
    crossfade_duration = int(sys.argv[4]) if len(sys.argv) > 4 else 0
    
    # Validate input files
    if not os.path.exists(mp3_file1):
        print(f"❌ Error: File '{mp3_file1}' does not exist")
        sys.exit(1)
        
    if not os.path.exists(mp3_file2):
        print(f"❌ Error: File '{mp3_file2}' does not exist")
        sys.exit(1)
    
    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(output_wav)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Perform the merge
    merge_mp3_to_wav(mp3_file1, mp3_file2, output_wav, crossfade_duration)


def interactive_mode():
    """Interactive mode for easier usage"""
    print("=== MP3 to WAV Merger ===")
    print()
    
    # Get input files
    mp3_file1 = input("Enter path to first MP3 file: ").strip().strip('"')
    if not os.path.exists(mp3_file1):
        print(f"❌ Error: File '{mp3_file1}' does not exist")
        return
    
    mp3_file2 = input("Enter path to second MP3 file: ").strip().strip('"')
    if not os.path.exists(mp3_file2):
        print(f"❌ Error: File '{mp3_file2}' does not exist")
        return
    
    # Get output file
    default_output = "merged_output.wav"
    output_wav = input(f"Enter output WAV file path (default: {default_output}): ").strip().strip('"')
    if not output_wav:
        output_wav = default_output
    
    # Get crossfade option
    crossfade_input = input("Enter crossfade duration in milliseconds (default: 0): ").strip()
    crossfade_duration = int(crossfade_input) if crossfade_input.isdigit() else 0
    
    print("\n" + "="*50)
    
    # Perform the merge
    merge_mp3_to_wav(mp3_file1, mp3_file2, output_wav, crossfade_duration)


if __name__ == "__main__":
    try:
        # Check if pydub is installed
        import pydub
    except ImportError:
        print("❌ Error: pydub library is required")
        print("Install with: pip install pydub")
        print("\nNote: You may also need to install ffmpeg:")
        print("  - Windows: Download from https://ffmpeg.org/download.html")
        print("  - macOS: brew install ffmpeg")
        print("  - Linux: sudo apt install ffmpeg")
        sys.exit(1)
    
    # Run interactive mode if no arguments provided
    if len(sys.argv) == 1:
        interactive_mode()
    else:
        main()
