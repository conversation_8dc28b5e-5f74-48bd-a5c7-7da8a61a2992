#!/usr/bin/env python3
"""
Simple Audio Modifier - Không cần ffmpeg
Tạo các biến thể của file âm thanh để tránh vi phạm bản quyền
"""

import os
import sys
import random
import struct
import wave
from pathlib import Path


class SimpleAudioModifier:
    def __init__(self):
        self.modifications_applied = []
    
    def read_mp3_info(self, file_path):
        """Đ<PERSON><PERSON> thông tin cơ bản của file MP3"""
        try:
            file_size = os.path.getsize(file_path)
            print(f"📁 File: {os.path.basename(file_path)}")
            print(f"📊 Size: {file_size / (1024*1024):.2f} MB")
            return file_size
        except Exception as e:
            print(f"❌ Error reading file info: {e}")
            return None
    
    def create_modified_copy(self, input_file, output_file):
        """
        Tạo bản sao đã được chỉnh sửa để tránh Content ID
        Sử dụng các kỹ thuật đơn giản không cần ffmpeg
        """
        try:
            print("🔄 Creating modified copy...")
            
            with open(input_file, 'rb') as infile:
                data = bytearray(infile.read())
            
            # Modification 1: Thêm silence ở đầu và cuối (thêm bytes 0)
            silence_bytes = b'\x00' * 4096  # 4KB silence
            modified_data = silence_bytes + data + silence_bytes
            self.modifications_applied.append("Added silence padding")
            
            # Modification 2: Thay đổi nhẹ metadata (nếu có)
            # Tìm và thay đổi ID3 tags
            if data.startswith(b'ID3'):
                print("🏷️ Modifying ID3 tags...")
                # Thay đổi một số bytes trong metadata
                for i in range(10, min(100, len(modified_data))):
                    if modified_data[i] != 0:
                        modified_data[i] = (modified_data[i] + 1) % 256
                        break
                self.modifications_applied.append("Modified metadata")
            
            # Modification 3: Thêm random bytes ở cuối (như watermark)
            random_suffix = bytes([random.randint(0, 255) for _ in range(64)])
            modified_data.extend(random_suffix)
            self.modifications_applied.append("Added random signature")
            
            # Modification 4: Thay đổi nhẹ một số bytes âm thanh
            # Chỉ thay đổi rất nhẹ để không ảnh hưởng chất lượng
            audio_start = 1024  # Bỏ qua header
            for i in range(5):  # Chỉ thay đổi 5 vị trí
                pos = audio_start + random.randint(0, len(data) - audio_start - 1000)
                if pos < len(modified_data):
                    original_byte = modified_data[pos]
                    # Thay đổi rất nhẹ (±1)
                    modified_data[pos] = max(0, min(255, original_byte + random.choice([-1, 1])))
            self.modifications_applied.append("Applied micro-adjustments")
            
            # Ghi file output
            with open(output_file, 'wb') as outfile:
                outfile.write(modified_data)
            
            return True
            
        except Exception as e:
            print(f"❌ Error creating modified copy: {e}")
            return False
    
    def create_reversed_intro(self, input_file, output_file):
        """
        Tạo phiên bản với intro ngược để tránh Content ID
        """
        try:
            print("🔄 Creating version with reversed intro...")
            
            with open(input_file, 'rb') as infile:
                data = bytearray(infile.read())
            
            # Tìm vị trí bắt đầu của audio data (sau header)
            audio_start = 1024
            intro_length = min(8192, len(data) - audio_start)  # 8KB intro
            
            if intro_length > 0:
                # Lấy phần intro
                intro_data = data[audio_start:audio_start + intro_length]
                
                # Đảo ngược intro (theo bytes)
                reversed_intro = intro_data[::-1]
                
                # Tạo data mới: header + reversed_intro + phần còn lại
                new_data = (data[:audio_start] + 
                           reversed_intro + 
                           data[audio_start + intro_length:])
                
                # Thêm silence padding
                silence = b'\x00' * 2048
                final_data = silence + new_data + silence
                
                with open(output_file, 'wb') as outfile:
                    outfile.write(final_data)
                
                self.modifications_applied.append("Reversed intro section")
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ Error creating reversed intro: {e}")
            return False
    
    def create_multiple_variants(self, input_file, base_name="modified"):
        """
        Tạo nhiều phiên bản khác nhau
        """
        variants = []
        
        print(f"🎵 Creating multiple variants of {os.path.basename(input_file)}...")
        print()
        
        # Variant 1: Basic modification
        output1 = f"{base_name}_v1.mp3"
        if self.create_modified_copy(input_file, output1):
            variants.append(output1)
            print(f"✅ Created: {output1}")
        
        # Reset modifications list
        self.modifications_applied = []
        
        # Variant 2: Reversed intro
        output2 = f"{base_name}_v2.mp3"
        if self.create_reversed_intro(input_file, output2):
            variants.append(output2)
            print(f"✅ Created: {output2}")
        
        # Variant 3: Double modification
        if variants:
            output3 = f"{base_name}_v3.mp3"
            if self.create_modified_copy(variants[0], output3):
                variants.append(output3)
                print(f"✅ Created: {output3}")
        
        return variants
    
    def show_recommendations(self):
        """
        Hiển thị khuyến nghị để tránh vi phạm bản quyền
        """
        print("\n" + "="*60)
        print("💡 KHUYẾN NGHỊ TRÁNH VI PHẠM BẢN QUYỀN YOUTUBE:")
        print("="*60)
        print("1. 🎬 Thêm nội dung video gốc (không chỉ âm thanh)")
        print("2. 🗣️ Thêm bình luận hoặc giải thích của bạn")
        print("3. ⏱️ Sử dụng các đoạn ngắn (dưới 30 giây)")
        print("4. 🎵 Kết hợp với nhạc nền gốc của bạn")
        print("5. 📝 Ghi rõ nguồn gốc trong mô tả")
        print("6. 🎭 Sử dụng cho mục đích giáo dục/phê bình")
        print("7. 🔄 Thay đổi tốc độ phát (0.9x - 1.1x)")
        print("8. 🎼 Thêm hiệu ứng âm thanh")
        print("9. 📺 Tuân thủ Fair Use guidelines")
        print("10. ⚖️ Xin phép tác giả nếu có thể")
        print("="*60)
        
        print("\n🔧 Modifications applied:")
        for mod in self.modifications_applied:
            print(f"   ✓ {mod}")


def main():
    """Main function"""
    
    if len(sys.argv) < 2:
        print("🎵 Simple Audio Modifier")
        print("Usage: python simple_audio_modifier.py <input_file> [output_base_name]")
        print("\nExample:")
        print('  python simple_audio_modifier.py "merged_songs.mp3"')
        print('  python simple_audio_modifier.py "merged_songs.mp3" "my_music"')
        sys.exit(1)
    
    input_file = sys.argv[1]
    base_name = sys.argv[2] if len(sys.argv) > 2 else "modified_audio"
    
    # Validate input file
    if not os.path.exists(input_file):
        print(f"❌ Error: File '{input_file}' does not exist")
        sys.exit(1)
    
    # Create modifier and process
    modifier = SimpleAudioModifier()
    
    # Show file info
    modifier.read_mp3_info(input_file)
    print()
    
    # Create variants
    variants = modifier.create_multiple_variants(input_file, base_name)
    
    if variants:
        print(f"\n✅ Successfully created {len(variants)} variants!")
        print("\n📁 Output files:")
        for variant in variants:
            size = os.path.getsize(variant) / (1024*1024)
            print(f"   📄 {variant} ({size:.2f} MB)")
        
        modifier.show_recommendations()
        
        print(f"\n🎉 Audio modification completed!")
        print("💡 Use these modified files for your YouTube content.")
        
    else:
        print("\n❌ No variants were created!")
        sys.exit(1)


if __name__ == "__main__":
    main()
