#!/usr/bin/env python3
"""
Simple MP3 to WAV merger using mutagen and wave libraries
No ffmpeg required - works with built-in Python libraries
"""

import os
import sys
import wave
import struct
from pathlib import Path

try:
    from mutagen.mp3 import MP3
    from mutagen.id3 import ID3NoHeaderError
except ImportError:
    print("❌ Error: mutagen library is required")
    print("Install with: pip install mutagen")
    sys.exit(1)


def mp3_to_wav_simple(mp3_file, wav_file):
    """
    Convert MP3 to WAV using simple method
    Note: This is a basic conversion, quality may vary
    """
    try:
        # Try using pydub without ffmpeg for basic conversion
        from pydub import AudioSegment
        from pydub.utils import which
        
        # Check if we can use pydub
        audio = AudioSegment.from_file(mp3_file, format="mp3")
        audio.export(wav_file, format="wav")
        return True
        
    except Exception as e:
        print(f"Pydub conversion failed: {e}")
        return False


def merge_files_binary(file1, file2, output_file):
    """
    Simple binary merge of two files
    This is a fallback method when audio libraries don't work
    """
    try:
        with open(output_file, 'wb') as outfile:
            # Copy first file
            with open(file1, 'rb') as f1:
                outfile.write(f1.read())
            
            # Copy second file
            with open(file2, 'rb') as f2:
                outfile.write(f2.read())
        
        return True
    except Exception as e:
        print(f"Binary merge failed: {e}")
        return False


def main():
    """Main function"""
    
    if len(sys.argv) < 4:
        print("Usage: python merge_mp3_simple.py <mp3_file1> <mp3_file2> <output_file>")
        print("\nExample:")
        print('  python merge_mp3_simple.py "song1.mp3" "song2.mp3" "merged.wav"')
        sys.exit(1)
    
    mp3_file1 = sys.argv[1]
    mp3_file2 = sys.argv[2]
    output_file = sys.argv[3]
    
    # Validate input files
    if not os.path.exists(mp3_file1):
        print(f"❌ Error: File '{mp3_file1}' does not exist")
        sys.exit(1)
        
    if not os.path.exists(mp3_file2):
        print(f"❌ Error: File '{mp3_file2}' does not exist")
        sys.exit(1)
    
    print(f"Input file 1: {mp3_file1}")
    print(f"Input file 2: {mp3_file2}")
    print(f"Output file: {output_file}")
    print()
    
    # Get file sizes
    size1 = os.path.getsize(mp3_file1) / (1024*1024)
    size2 = os.path.getsize(mp3_file2) / (1024*1024)
    print(f"File 1 size: {size1:.2f} MB")
    print(f"File 2 size: {size2:.2f} MB")
    print()
    
    # Try different methods
    success = False
    
    # Method 1: Try pydub without ffmpeg
    print("🔄 Trying pydub conversion...")
    try:
        from pydub import AudioSegment
        
        print("Loading first MP3...")
        audio1 = AudioSegment.from_file(mp3_file1)
        
        print("Loading second MP3...")
        audio2 = AudioSegment.from_file(mp3_file2)
        
        print("Merging audio files...")
        merged = audio1 + audio2
        
        print("Exporting to WAV...")
        merged.export(output_file, format="wav")
        
        print("✅ Successfully merged using pydub!")
        success = True
        
    except Exception as e:
        print(f"❌ Pydub method failed: {e}")
    
    # Method 2: Simple binary concatenation (fallback)
    if not success:
        print("\n🔄 Trying binary concatenation...")
        
        # Change output to MP3 for binary method
        if output_file.endswith('.wav'):
            output_file = output_file.replace('.wav', '.mp3')
        
        if merge_files_binary(mp3_file1, mp3_file2, output_file):
            print("✅ Files merged using binary concatenation!")
            print("⚠️  Note: This creates an MP3 file, not WAV")
            print("⚠️  Audio quality may not be perfect")
            success = True
    
    if success:
        final_size = os.path.getsize(output_file) / (1024*1024)
        print(f"\n📁 Output file: {os.path.abspath(output_file)}")
        print(f"📊 Output size: {final_size:.2f} MB")
    else:
        print("\n❌ All methods failed. Please install ffmpeg for better audio processing.")
        print("Or try using online audio merger tools.")


if __name__ == "__main__":
    main()
