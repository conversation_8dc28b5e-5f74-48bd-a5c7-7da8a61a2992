#!/usr/bin/env python3
"""
Copyright Avoidance Techniques - Giải thích chi tiết và demo
"""

import os
import sys
import random
import hashlib
import time


def explain_copyright_detection():
    """Giải thích cách Content ID hoạt động"""
    print("🔍 CÁCH YOUTUBE CONTENT ID HOẠT ĐỘNG:")
    print("="*60)
    
    print("\n📊 PHƯƠNG PHÁP NHẬN DIỆN:")
    methods = {
        "Audio Fingerprinting": "Tạo 'dấu vân tay' từ đặc trưng âm thanh",
        "Waveform Analysis": "Phân tích dạng sóng âm thanh",
        "Spectral Analysis": "Phân tích phổ tần số",
        "Tempo Detection": "Nhận diện nhịp độ và beat",
        "Pitch Detection": "Nhận diện cao độ và melody",
        "Metadata Matching": "So sánh thông tin file (tên, tác giả)",
        "Duration Matching": "So sánh thời lượng",
        "Silence Pattern": "Phân tích pattern im lặng"
    }
    
    for method, description in methods.items():
        print(f"   🎵 {method}: {description}")
    
    print("\n🧠 CONTENT ID TẠO FINGERPRINT TỪ:")
    fingerprint_elements = [
        "Tần số dominant trong từng khoảng thời gian",
        "Cường độ âm thanh (amplitude) theo thời gian", 
        "Chuyển đổi tần số (frequency transitions)",
        "Pattern nhịp điệu và beat",
        "Cấu trúc intro-verse-chorus-outro",
        "Đặc trưng harmonic và overtones"
    ]
    
    for element in fingerprint_elements:
        print(f"   📈 {element}")


def explain_avoidance_techniques():
    """Giải thích các kỹ thuật tránh phát hiện"""
    print("\n🛡️ CÁC KỸ THUẬT TRÁNH PHÁT HIỆN:")
    print("="*60)
    
    techniques = {
        "1. TIMING MODIFICATIONS": [
            "🕐 Thêm silence ở đầu/cuối (thay đổi timing)",
            "⏱️ Chèn micro-gaps vào giữa bài",
            "🔄 Thay đổi tốc độ phát (0.9x - 1.1x)",
            "📏 Cắt bỏ/thêm các đoạn nhỏ"
        ],
        
        "2. FREQUENCY MODIFICATIONS": [
            "🎼 Thay đổi pitch (±0.5-2 semitones)",
            "🔊 Điều chỉnh EQ (bass, treble)",
            "📊 Thay đổi sample rate",
            "🎚️ Normalize volume levels"
        ],
        
        "3. AUDIO STRUCTURE CHANGES": [
            "🔀 Đảo ngược intro/outro",
            "✂️ Cắt và sắp xếp lại sections",
            "🎵 Thêm fade in/out",
            "🔄 Hoán đổi left/right channels"
        ],
        
        "4. METADATA OBFUSCATION": [
            "🏷️ Xóa/thay đổi ID3 tags",
            "📝 Thay đổi file properties",
            "🔢 Thêm unique identifiers",
            "📅 Thay đổi timestamps"
        ],
        
        "5. CONTENT TRANSFORMATION": [
            "🎭 Thêm commentary/narration",
            "🎬 Kết hợp với visual content",
            "🎵 Mix với nhạc gốc (50/50)",
            "🗣️ Overlay với giọng nói"
        ],
        
        "6. MICRO-LEVEL CHANGES": [
            "🔬 Thay đổi amplitude nhẹ (±1-2 levels)",
            "📡 Thêm inaudible noise",
            "🌊 Micro-adjustments trong waveform",
            "🎯 Thay đổi bit depth"
        ]
    }
    
    for category, methods in techniques.items():
        print(f"\n{category}:")
        for method in methods:
            print(f"   {method}")


def create_simple_safe_version(input_file, output_file):
    """Tạo phiên bản đơn giản tránh bản quyền"""
    try:
        print(f"\n🔧 Creating safe version: {output_file}")
        
        with open(input_file, 'rb') as f:
            data = f.read()
        
        # Tạo modified data
        modified = bytearray()
        
        # 1. Thêm silence ở đầu
        intro_silence = b'\x00' * random.randint(4096, 8192)
        modified.extend(intro_silence)
        print(f"   ✅ Added {len(intro_silence)} bytes intro silence")
        
        # 2. Thêm original data
        modified.extend(data)
        
        # 3. Thay đổi một số bytes nhẹ
        changes = 0
        for i in range(1024, len(modified), 1000):
            if i < len(modified) and changes < 50:
                original = modified[i]
                change = random.choice([-1, 0, 1])
                modified[i] = max(0, min(255, original + change))
                changes += 1
        print(f"   ✅ Applied {changes} micro-changes")
        
        # 4. Thêm unique signature
        timestamp = int(time.time())
        signature = f"_SAFE_{timestamp}_{random.randint(1000,9999)}_".encode()
        modified.extend(b'\x00' * 32 + signature + b'\x00' * 32)
        print(f"   ✅ Added unique signature")
        
        # 5. Thêm silence ở cuối
        outro_silence = b'\x00' * random.randint(2048, 4096)
        modified.extend(outro_silence)
        print(f"   ✅ Added {len(outro_silence)} bytes outro silence")
        
        # Ghi file
        with open(output_file, 'wb') as f:
            f.write(modified)
        
        original_size = len(data) / (1024*1024)
        new_size = len(modified) / (1024*1024)
        
        print(f"   📊 Original: {original_size:.2f} MB")
        print(f"   📊 Modified: {new_size:.2f} MB")
        print(f"   📊 Change: +{new_size - original_size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


def show_legal_guidelines():
    """Hiển thị hướng dẫn pháp lý"""
    print("\n⚖️ HƯỚNG DẪN PHÁP LÝ VÀ FAIR USE:")
    print("="*60)
    
    print("\n📖 FAIR USE DOCTRINE (Mỹ):")
    fair_use_factors = [
        "🎯 Mục đích sử dụng (giáo dục, phê bình, bình luận)",
        "📚 Bản chất của tác phẩm gốc",
        "📏 Lượng nội dung được sử dụng",
        "💰 Tác động đến thị trường của tác phẩm gốc"
    ]
    
    for factor in fair_use_factors:
        print(f"   {factor}")
    
    print("\n✅ CÁCH SỬ DỤNG AN TOÀN:")
    safe_practices = [
        "🎓 Sử dụng cho mục đích giáo dục",
        "💬 Thêm bình luận/phân tích substantial",
        "⏱️ Sử dụng clips ngắn (dưới 30 giây)",
        "🎭 Tạo parody hoặc satire",
        "📝 Credit đầy đủ nguồn gốc",
        "🔄 Transform nội dung đáng kể",
        "🎵 Kết hợp với nội dung gốc",
        "📺 Tuân thủ guidelines của platform"
    ]
    
    for practice in safe_practices:
        print(f"   {practice}")
    
    print("\n🚨 LƯU Ý QUAN TRỌNG:")
    warnings = [
        "⚖️ Luật bản quyền khác nhau ở mỗi quốc gia",
        "🏛️ Fair Use chỉ áp dụng ở một số quốc gia",
        "💼 Tham khảo luật sư cho việc thương mại",
        "🎵 Ưu tiên sử dụng nhạc royalty-free",
        "📧 Liên hệ chủ sở hữu bản quyền khi có thể",
        "🔍 Kiểm tra tình trạng bản quyền trước khi sử dụng"
    ]
    
    for warning in warnings:
        print(f"   {warning}")


def show_youtube_specific_tips():
    """Tips đặc biệt cho YouTube"""
    print("\n🎬 TIPS ĐẶC BIỆT CHO YOUTUBE:")
    print("="*60)
    
    youtube_tips = [
        "🎥 Thêm visual content (không chỉ audio)",
        "🗣️ Record reaction/commentary",
        "📱 Sử dụng YouTube's Audio Library",
        "🎵 Mix với background music từ YouTube",
        "📝 Viết description chi tiết về fair use",
        "🏷️ Sử dụng tags phù hợp",
        "⏰ Upload vào giờ ít traffic",
        "🔒 Bắt đầu với unlisted/private",
        "📊 Monitor Content ID claims",
        "💬 Engage với audience để tăng transformative value"
    ]
    
    for tip in youtube_tips:
        print(f"   {tip}")


def main():
    """Main function"""
    
    print("🛡️ COPYRIGHT AVOIDANCE TECHNIQUES GUIDE")
    print("="*70)
    
    # Giải thích detection
    explain_copyright_detection()
    
    # Giải thích techniques
    explain_avoidance_techniques()
    
    # Nếu có file input, tạo demo
    if len(sys.argv) >= 2:
        input_file = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else "demo_safe_output.mp3"
        
        if os.path.exists(input_file):
            success = create_simple_safe_version(input_file, output_file)
            if success:
                print(f"\n✅ Demo file created: {output_file}")
        else:
            print(f"\n❌ File not found: {input_file}")
    
    # Hiển thị guidelines
    show_legal_guidelines()
    show_youtube_specific_tips()
    
    print("\n" + "="*70)
    print("📚 DISCLAIMER: This is for educational purposes only.")
    print("Always respect copyright laws and platform guidelines.")
    print("Consider using original or royalty-free content when possible.")
    print("="*70)


if __name__ == "__main__":
    main()
